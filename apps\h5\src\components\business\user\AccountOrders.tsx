'use client'

import { useCallback, useEffect, useMemo, useRef, useState } from 'react'
import { useSearchParams } from 'next/navigation'
import { useTranslations } from 'next-intl'
import empty_order from '@public/images/empty_order.png'
import {
  generateOSSUrl,
  OrderListItem,
  resolveCatchMessage,
  ROUTE,
  setOrderFilterStatus,
  TRACK_EVENT,
  useGetOrderStatusQuery,
  useLazyGetAfterSalesQuery,
  useLazyGetOrdersQuery,
  useLoadingContext,
  usePagination,
  userCancelOrderIdSelector,
  userOrderFilterStatusSelector,
  useToastContext,
  useVolcAnalytics,
} from '@ninebot/core'
import { useAppDispatch, useAppSelector } from '@ninebot/core/src/store/hooks'
import { DotLoading, Tabs } from 'antd-mobile'

import { JumpToCustomerService } from '@/businessComponents'
import { CustomButton, CustomEmpty, CustomNavBar, IconService } from '@/components'
import { useRouter } from '@/i18n/navigation'

import { OrderItem, OrderSkeleton } from './components'
const AFTER_SALES = 'order_requisition'

type TabItem = {
  label?: string | null
  status?: string | null
}

const AccountOrders = () => {
  const { reportEvent } = useVolcAnalytics()
  const searchParams = useSearchParams()
  const orderStatus = useAppSelector(userOrderFilterStatusSelector)
  const cancelOrderNumber = useAppSelector(userCancelOrderIdSelector)
  const [activeKey, setActiveKey] = useState(orderStatus)
  const currentTime = useRef(new Date().getTime())

  const loading = useLoadingContext()
  const toast = useToastContext()
  const getI18nString = useTranslations('Common')
  const router = useRouter()
  const dispatch = useAppDispatch()

  const { page, pageSize, handlePageNext, handlePageChange } = usePagination()

  // 使用Map来缓存不同状态的订单数据，避免切换状态时清空数据导致倒计时重置
  const [ordersMap, setOrdersMap] = useState<Record<string, OrderListItem[]>>({})
  const [tabs, setTabs] = useState<TabItem[]>([])

  const [hasMore, setHasMore] = useState(true)
  const [isPullRefreshing, setIsPullRefreshing] = useState(false)

  // 根据当前activeKey获取对应的订单数据
  const orders = useMemo(() => {
    return ordersMap[activeKey] || []
  }, [ordersMap, activeKey])

  const { data: orderStatusData, isLoading: orderStatusLoading } = useGetOrderStatusQuery({})
  const [getOrders, { isFetching: orderFetching }] = useLazyGetOrdersQuery()
  const [getAfterSales, { isFetching: afterSalesFetching }] = useLazyGetAfterSalesQuery()

  /**
   * 切换订单筛选状态
   */
  const handleTabChange = useCallback(
    (key: string) => {
      dispatch(setOrderFilterStatus(key))
      setActiveKey(key)
      // 更新当前时间，确保倒计时基于最新时间计算
      currentTime.current = new Date().getTime()
    },
    [dispatch],
  )

  /**
   * 格式化 Tabs
   */
  const formatTabs = useCallback(
    (
      data: {
        label?: string | null
        status?: string | null
      }[],
    ) =>
      data.map((i) => ({
        label: i.label,
        status: i.status,
      })),
    [],
  )

  /**
   * 页面初始 Loading
   */
  const isInitLoading = useMemo(() => orderStatusLoading, [orderStatusLoading])

  /**
   * 查看更多 Loading
   */
  const isMoreLoading = useMemo(() => page !== 1 && orderFetching, [page, orderFetching])

  /**
   * 切换订单状态 Loading
   */
  const isFilterLoading = useMemo(
    () =>
      page === 1 && !isInitLoading && !isPullRefreshing && (orderFetching || afterSalesFetching),
    [page, isInitLoading, isPullRefreshing, orderFetching, afterSalesFetching],
  )

  /**
   * 获取订单列表
   */
  const fetchOrders = useCallback(() => {
    if (activeKey === AFTER_SALES) {
      return
    }
    getOrders({
      filter: {
        status: {
          eq: activeKey,
        },
      },
      currentPage: page,
      pageSize: pageSize,
    })
      .unwrap()
      .then((res) => {
        const ordersData = res?.customer?.orders?.items || []
        // 使用缓存机制，避免直接替换数据导致组件重新挂载
        setOrdersMap((prev) => {
          if (page === 1) {
            return {
              ...prev,
              [activeKey]: ordersData as OrderListItem[],
            }
          } else {
            const existingOrders = prev[activeKey] || []
            return {
              ...prev,
              [activeKey]: [...existingOrders, ...(ordersData as OrderListItem[])],
            }
          }
        })
        if (ordersData.length < pageSize) {
          setHasMore(false)
        } else {
          setHasMore(true)
        }
      })
      .catch((error) => {
        toast.show({
          icon: 'fail',
          content: resolveCatchMessage(error) as string,
        })
      })
      .finally(() => {
        setIsPullRefreshing(false)
      })
  }, [activeKey, getOrders, page, pageSize, toast])

  /**
   * 获取售后列表
   */
  const fetchAfterSales = useCallback(() => {
    if (activeKey !== AFTER_SALES) {
      return
    }
    getAfterSales({
      currentPage: page,
      pageSize: pageSize,
    })
      .unwrap()
      .then((res) => {
        const ordersData = res?.requisitionList?.items || []
        // 使用缓存机制，避免直接替换数据导致组件重新挂载
        setOrdersMap((prev) => {
          if (page === 1) {
            return {
              ...prev,
              [activeKey]: ordersData as OrderListItem[],
            }
          } else {
            const existingOrders = prev[activeKey] || []
            return {
              ...prev,
              [activeKey]: [...existingOrders, ...(ordersData as OrderListItem[])],
            }
          }
        })
        if (ordersData.length < pageSize) {
          setHasMore(false)
        } else {
          setHasMore(true)
        }
      })
      .catch((error) => {
        toast.show({
          icon: 'fail',
          content: resolveCatchMessage(error) as string,
        })
      })
      .finally(() => {
        setIsPullRefreshing(false)
      })
  }, [activeKey, getAfterSales, page, pageSize, toast])

  /**
   * 自定义返回按钮操作事件
   */
  const handleNavLeftPress = useCallback(() => {
    const prevRoute = searchParams.get('from') || ''

    // 从以下页面到当前页面的流程，返回到首页
    if ([ROUTE.accountOrderDetail].includes(prevRoute)) {
      router.replace('/')
      return
    }

    // 默认返回行为
    router.back()
  }, [router, searchParams])

  const renderRight = useCallback(() => {
    return (
      <div className="float-end">
        <JumpToCustomerService
          udeskParams={{
            type: 'order',
            targetId: '',
          }}>
          <IconService />
        </JumpToCustomerService>
      </div>
    )
  }, [])

  /**
   * 修改订单状态为 '已取消'
   */
  const updateCancelOrderStatus = useCallback(
    (number: string) => {
      setOrdersMap((prev) => {
        const currentOrders = prev[activeKey] || []
        return {
          ...prev,
          [activeKey]: currentOrders.map((item) =>
            item?.number === number
              ? {
                  ...item,
                  status_code: 'canceled',
                  status_tab_label: '取消',
                }
              : item,
          ),
        }
      })
    },
    [activeKey],
  )

  /**
   * 重置分页并拉取数据
   */
  const handleRefresh = () => {
    if (page === 1) {
      if (activeKey === AFTER_SALES) {
        fetchAfterSales()
      } else {
        fetchOrders()
      }
    } else {
      handlePageChange(1)
    }
  }

  /**
   * 下拉刷新
   */
  // const handlePullRefresh = () => {
  //   setIsPullRefreshing(true);
  //   currentTime.current = new Date().getTime();
  //   handleRefresh();
  // };

  /**
   * 切换订单状态时重置分页
   */
  useEffect(() => {
    handlePageChange(1)
  }, [handlePageChange, activeKey])

  /**
   * 请求订单列表
   */
  useEffect(() => {
    if (activeKey !== undefined && activeKey !== null) {
      fetchOrders()
    }
  }, [activeKey, fetchOrders])

  useEffect(() => {
    if (activeKey === AFTER_SALES) {
      fetchAfterSales()
    }
  }, [activeKey, fetchAfterSales])

  /**
   * 获取订单状态
   */
  useEffect(() => {
    if (
      orderStatusData?.customer?.orders?.status_tab &&
      orderStatusData?.customer?.orders?.status_tab?.length > 0
    ) {
      setTabs(formatTabs((orderStatusData.customer.orders.status_tab as TabItem[]) || []))
    }
  }, [orderStatusData, formatTabs])

  /**
   * 修改被取消订单状态
   */
  useEffect(() => {
    if (cancelOrderNumber) {
      setOrdersMap((prev) => {
        const currentOrders = prev[activeKey] || []
        return {
          ...prev,
          [activeKey]: currentOrders.map((order) =>
            order?.number === cancelOrderNumber
              ? {
                  ...order,
                  status_code: 'canceled',
                  status_tab_label: getI18nString('cancelled'),
                }
              : order,
          ),
        }
      })
    }
  }, [cancelOrderNumber, getI18nString, activeKey])

  /**
   * 筛选订单 Loading
   */
  useEffect(() => {
    if (isFilterLoading) {
      loading.show()
    } else {
      loading.hide()
    }
  }, [loading, isFilterLoading])

  /**
   * 处理页面可见性变化，确保倒计时在切换后台后回到前台时显示正确时间
   */
  useEffect(() => {
    const handleVisibilityChange = () => {
      if (document.visibilityState === 'visible') {
        currentTime.current = new Date().getTime()
      }
    }

    document.addEventListener('visibilitychange', handleVisibilityChange)

    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange)
    }
  }, [])

  /**
   * 埋点：点击我的订单
   */
  useEffect(() => {
    reportEvent(TRACK_EVENT.shop_order_page_exposure)
  }, [reportEvent])

  return (
    <div className="flex flex-col">
      <CustomNavBar
        title={getI18nString('my_orders')}
        right={renderRight()}
        onBack={handleNavLeftPress}
      />
      {isInitLoading ? (
        <OrderSkeleton />
      ) : (
        <>
          <Tabs
            className="order-tabs"
            style={{
              '--active-title-color': '#DA291C',
              '--active-line-height': '0',
              position: 'sticky',
              top: 52,
              backgroundColor: '#F3F3F4',
              padding: '0 1.6rem',
              zIndex: 50,
            }}
            defaultActiveKey={activeKey}
            onChange={handleTabChange}
            stretch={false}>
            {tabs.map((item) => (
              <Tabs.Tab key={item.status} title={item.label} />
            ))}
          </Tabs>
          <div className="min-h-[calc(100vh-116px)] bg-gray-base px-[12px] pb-[12px]">
            {orders.length ? (
              <div className="flex flex-col gap-[8px] bg-gray-base">
                {orders.map((order) => (
                  <OrderItem
                    key={order?.id}
                    order={order}
                    currentTime={currentTime?.current}
                    toast={toast}
                    updateCancelOrderStatus={updateCancelOrderStatus}
                    handleRefresh={handleRefresh}
                  />
                ))}
                {hasMore ? (
                  isMoreLoading ? (
                    <div className="flex h-40 flex-col items-center justify-center">
                      <DotLoading />
                    </div>
                  ) : (
                    <div className="flex h-40 flex-col items-center justify-center">
                      <CustomButton
                        customStyle={{
                          width: 82,
                          borderRadius: 16,
                          paddingTop: 8,
                          paddingBottom: 8,
                          backgroundColor: '#F8F8F9',
                        }}
                        onClick={handlePageNext}>
                        <div className="text-center font-miSansDemiBold450 text-[13px] leading-[16px] text-[#000000]">
                          {getI18nString('more')}
                        </div>
                      </CustomButton>
                    </div>
                  )
                ) : null}
              </div>
            ) : (
              <div className="flex min-h-[calc(100vh-161px)] flex-col rounded-[16px] bg-white pt-[112px]">
                <CustomEmpty
                  source={generateOSSUrl('images/empty_order.png') || empty_order.src}
                  description={getI18nString('no_orders')}
                />
              </div>
            )}
          </div>
        </>
      )}
    </div>
  )
}

export default AccountOrders
