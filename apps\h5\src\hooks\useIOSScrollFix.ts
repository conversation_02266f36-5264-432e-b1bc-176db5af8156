/**
 * iOS滚动修复Hook
 * 专门处理iPhone浏览器快速滑动时的白屏问题
 */

import { useEffect, useRef } from 'react'

import {
  applyIOSScrollOptimization,
  detectAndFixWhiteScreen,
  gentleForceRepaint,
  isIOS,
  isSafari,
  removeIOSScrollOptimization,
} from '@/utils/iosScrollOptimization'

interface UseIOSScrollFixOptions {
  /** 是否启用自动白屏检测 */
  enableWhiteScreenDetection?: boolean
  /** 白屏检测延迟时间(ms) */
  detectionDelay?: number
  /** 是否在滚动结束后强制重绘 */
  forceRepaintOnScrollEnd?: boolean
  /** 滚动结束检测延迟时间(ms) */
  scrollEndDelay?: number
}

/**
 * iOS滚动修复Hook
 *
 * @param options 配置选项
 * @returns containerRef - 需要应用到容器元素的ref
 *
 * @example
 * ```tsx
 * const { containerRef } = useIOSScrollFix({
 *   enableWhiteScreenDetection: true,
 *   forceRepaintOnScrollEnd: true
 * })
 *
 * return (
 *   <div ref={containerRef} className="ios-scroll-fix">
 *     {content}
 *   </div>
 * )
 * ```
 */
export const useIOSScrollFix = (options: UseIOSScrollFixOptions = {}) => {
  const {
    enableWhiteScreenDetection = true,
    detectionDelay = 100,
    forceRepaintOnScrollEnd = true,
    scrollEndDelay = 150,
  } = options

  const containerRef = useRef<HTMLDivElement>(null)
  const scrollTimeoutRef = useRef<NodeJS.Timeout | null>(null)
  const isScrollingRef = useRef(false)

  // 滚动结束检测和修复
  const handleScrollEnd = () => {
    if (!forceRepaintOnScrollEnd || !isIOS()) return

    if (scrollTimeoutRef.current) {
      clearTimeout(scrollTimeoutRef.current)
    }

    scrollTimeoutRef.current = setTimeout(() => {
      isScrollingRef.current = false

      // 滚动结束后强制重绘，防止白屏
      gentleForceRepaint(containerRef.current || undefined)

      // 额外的白屏检测
      if (enableWhiteScreenDetection) {
        detectAndFixWhiteScreen()
      }
    }, scrollEndDelay)
  }

  // 滚动开始检测
  const handleScrollStart = () => {
    isScrollingRef.current = true

    if (scrollTimeoutRef.current) {
      clearTimeout(scrollTimeoutRef.current)
    }
  }

  useEffect(() => {
    // 只在iOS设备上启用
    if (!isIOS()) return

    const container = containerRef.current
    if (!container) return

    // 应用iOS滚动优化样式
    applyIOSScrollOptimization(container)

    // 初始白屏检测
    if (enableWhiteScreenDetection) {
      const initialCheckTimer = setTimeout(() => {
        detectAndFixWhiteScreen()
      }, detectionDelay)

      // 页面加载完成后再次检测
      const secondCheckTimer = setTimeout(() => {
        detectAndFixWhiteScreen()
      }, detectionDelay * 5)

      return () => {
        clearTimeout(initialCheckTimer)
        clearTimeout(secondCheckTimer)
      }
    }

    return () => {
      if (container) {
        removeIOSScrollOptimization(container)
      }
    }
  }, [enableWhiteScreenDetection, detectionDelay])

  // 滚动事件监听
  useEffect(() => {
    if (!isIOS() || !forceRepaintOnScrollEnd) return

    const handleScroll = () => {
      handleScrollStart()
      handleScrollEnd()
    }

    // 使用passive监听器优化性能
    window.addEventListener('scroll', handleScroll, { passive: true })

    // 监听触摸事件，更精确地检测滚动状态
    window.addEventListener('touchstart', handleScrollStart, { passive: true })
    window.addEventListener('touchend', handleScrollEnd, { passive: true })

    return () => {
      window.removeEventListener('scroll', handleScroll)
      window.removeEventListener('touchstart', handleScrollStart)
      window.removeEventListener('touchend', handleScrollEnd)

      if (scrollTimeoutRef.current) {
        clearTimeout(scrollTimeoutRef.current)
      }
    }
  }, [forceRepaintOnScrollEnd, scrollEndDelay])

  // 页面可见性变化时的修复
  useEffect(() => {
    if (!isIOS() || !enableWhiteScreenDetection) return

    const handleVisibilityChange = () => {
      if (document.visibilityState === 'visible') {
        // 页面重新可见时检测白屏
        setTimeout(() => {
          detectAndFixWhiteScreen()
        }, 100)
      }
    }

    document.addEventListener('visibilitychange', handleVisibilityChange)

    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange)
    }
  }, [enableWhiteScreenDetection])

  // 内存压力检测和优化
  useEffect(() => {
    if (!isIOS() || !isSafari()) return

    // 监听内存警告（如果支持）
    const handleMemoryWarning = () => {
      console.warn('Memory pressure detected, applying fixes...')

      // 强制垃圾回收（如果可能）
      if (window.gc) {
        window.gc()
      }

      // 强制重绘
      gentleForceRepaint()

      // 清理可能的缓存
      if (containerRef.current) {
        const container = containerRef.current
        // 临时移除和重新应用优化
        removeIOSScrollOptimization(container)
        setTimeout(() => {
          applyIOSScrollOptimization(container)
        }, 50)
      }
    }

    // 监听内存压力事件（如果支持）
    if ('memory' in performance && 'addEventListener' in performance) {
      // @ts-ignore - 这是实验性API
      performance.addEventListener('memory', handleMemoryWarning)

      return () => {
        // @ts-ignore
        performance.removeEventListener('memory', handleMemoryWarning)
      }
    }
  }, [])

  return {
    containerRef,
    isScrolling: isScrollingRef.current,
    forceRepaint: () => gentleForceRepaint(containerRef.current || undefined),
    detectWhiteScreen: detectAndFixWhiteScreen,
  }
}

/**
 * 简化版的iOS滚动修复Hook
 * 只包含基本的白屏修复功能
 */
export const useSimpleIOSScrollFix = () => {
  return useIOSScrollFix({
    enableWhiteScreenDetection: true,
    forceRepaintOnScrollEnd: false,
    detectionDelay: 50,
  })
}

/**
 * 高性能版的iOS滚动修复Hook
 * 包含所有优化功能，适用于复杂页面
 */
export const useAdvancedIOSScrollFix = () => {
  return useIOSScrollFix({
    enableWhiteScreenDetection: true,
    forceRepaintOnScrollEnd: true,
    detectionDelay: 100,
    scrollEndDelay: 100,
  })
}
