'use client'

import { useCallback, useEffect, useRef, useState } from 'react'

import {
  createOptimizedScrollListener,
  customSmoothScroll,
  gentleForceRepaint,
  isIOS,
} from '@/utils/iosScrollOptimization'

const FloatingButtons = () => {
  const [showScrollTop, setShowScrollTop] = useState(false)
  const scrollListenerRef = useRef<{ cleanup: () => void } | null>(null)

  // 优化的滚动事件处理
  const handleScroll = useCallback(() => {
    try {
      // 当页面滚动超过300px时显示按钮
      setShowScrollTop(window.scrollY > 300)
    } catch (error) {
      console.warn('Error in floating buttons scroll handler:', error)
      // iOS白屏修复
      if (isIOS()) {
        gentleForceRepaint()
      }
    }
  }, [])

  // 处理滚动事件
  useEffect(() => {
    // 清理之前的监听器
    if (scrollListenerRef.current) {
      scrollListenerRef.current.cleanup()
    }

    // 创建优化的滚动监听器
    scrollListenerRef.current = createOptimizedScrollListener(handleScroll, {
      throttleMs: 100, // 降低频率，因为这个组件不需要高频更新
      passive: true,
    })

    return () => {
      if (scrollListenerRef.current) {
        scrollListenerRef.current.cleanup()
        scrollListenerRef.current = null
      }
    }
  }, [handleScroll])

  const handleScrollToTop = useCallback(() => {
    // 使用自定义平滑滚动，避免iOS问题
    customSmoothScroll(0, 600, () => {
      // 滚动完成后的iOS白屏修复
      if (isIOS()) {
        setTimeout(() => {
          gentleForceRepaint()
        }, 50)
      }
    })
  }, [])

  return (
    <div className="fixed bottom-[120px] right-base-16 z-10 flex flex-col gap-10">
      {showScrollTop && (
        <button
          onClick={handleScrollToTop}
          className="flex h-[32px] w-[32px] items-center justify-center rounded-[8px] transition-opacity hover:opacity-80"
          style={{
            backgroundColor: 'rgba(0, 0, 0, 0.65)',
            backdropFilter: 'blur(10px)',
            WebkitBackdropFilter: 'blur(10px)',
          }}
          aria-label="返回顶部">
          <svg
            width="16"
            height="16"
            viewBox="0 0 16 16"
            fill="none"
            xmlns="http://www.w3.org/2000/svg">
            <path
              d="M1.50184 9.30299L2.5625 10.3672L7.8658 5.04618L7.8658 3.98198L6.80514 3.98198L1.50184 9.30299Z"
              fill="white"
            />
            <path
              d="M14.8849 9.30299L13.8242 10.3672L8.52092 5.04618L8.52092 3.98198L9.58158 3.98198L14.8849 9.30299Z"
              fill="white"
            />
            <path
              d="M8.94531 15.502H7.44531V6.60206L8.19531 5.84956L8.94531 6.60206L8.94531 15.502Z"
              fill="white"
            />
            <path
              d="M15.5 0.499876L15.5 2.00488L8.75 2.00488L7.16667 0.500976L8.75 0.499875L15.5 0.499876Z"
              fill="white"
            />
            <path
              d="M0.5 0.499876L0.5 2.00488L7.25 2.00488L9.25 2.00488L7.25 0.499875L0.5 0.499876Z"
              fill="white"
            />
          </svg>
        </button>
      )}
    </div>
  )
}

export default FloatingButtons
