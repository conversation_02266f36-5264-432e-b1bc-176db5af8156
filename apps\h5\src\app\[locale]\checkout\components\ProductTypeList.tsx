'use client'

import {
  forwardRef,
  useCallback,
  useEffect,
  useImperativeHandle,
  useMemo,
  useRef,
  useState,
} from 'react'
import { useTranslations } from 'next-intl'
import { yupResolver } from '@hookform/resolvers/yup'
import { OrderPickupStore, useToastContext } from '@ninebot/core'
import { pickUpCommentSchema } from '@ninebot/core/src/validations'
import { Controller, useForm } from 'react-hook-form'

import { PickupStoreCard } from '@/components'
import type { RawProduct } from '@/types/cart'
import { CartItem } from '@/types/checkout'

import ProductView from './ProductView'

interface FormData {
  telephone: string
  comment?: string
}

interface ProductTypeListRef {
  validate: () => Promise<{ [key: string]: string | number | boolean } | null>
  scrollTo: () => Promise<void>
}

interface ProductTypeListProps {
  showType?: 'express' | 'pickup'
  scrollViewRef?: React.RefObject<HTMLDivElement>
  title: string
  products: CartItem[]
}

// 使用 forwardRef 包装组件，正确处理 ref 传递
export default forwardRef<ProductTypeListRef, ProductTypeListProps>(function ProductTypeList(
  { showType = 'express', scrollViewRef, title, products },
  ref,
) {
  const toast = useToastContext()
  const rootRef = useRef<HTMLDivElement>(null)
  const getI18nString = useTranslations('Common')

  // 跟踪用户是否已经开始与输入框交互
  const [hasInteracted, setHasInteracted] = useState(false)

  /**
   * 提供表单服务
   */
  const {
    control,
    trigger,
    clearErrors,
    getValues,
    formState: { errors },
  } = useForm<FormData>({
    mode: 'onBlur',
    resolver: yupResolver(pickUpCommentSchema),
    defaultValues: {
      telephone: '',
      comment: '',
    },
  })

  /**
   * 获取店铺信息
   */
  const pickupStoreInfo = useMemo(() => {
    return products?.length
      ? (products[0]?.extension_info as { store_info?: OrderPickupStore })?.store_info
      : null
  }, [products])

  /**
   * 自定义输入框失焦验证
   */
  const onBlurHandler = useCallback(
    async (name: keyof FormData) => {
      clearErrors()
      await trigger(name)
    },
    [clearErrors, trigger],
  )

  /**
   * 生成提交数据
   */
  const generateSubmitData = useCallback(
    (formData: FormData) => {
      const items = products.map((product) => ({
        item_id: product.uid,
        telephone: formData.telephone,
        comment: formData.comment,
      }))
      return {
        items: JSON.stringify(items),
      }
    },
    [products],
  )

  /**
   * 表单 errors 提示
   */
  useEffect(() => {
    if (errors && Object.keys(errors).length) {
      const firstErrorKey = Object.keys(errors)[0] as keyof FormData
      const firstError = errors[firstErrorKey]
      if (firstError?.message) {
        toast.show({
          content: firstError.message,
        })
      }
    }
  }, [errors, toast])

  /**
   * 暴露 Api
   */
  useImperativeHandle(ref, () => {
    return {
      /**
       * 验证表单
       */
      validate: async () => {
        const isValid = await trigger()
        if (isValid) {
          return generateSubmitData(getValues())
        }
        return null
      },
      /**
       * 滚动到表单位置
       */
      scrollTo: async () => {
        if (rootRef.current && scrollViewRef?.current) {
          const rect = rootRef.current.getBoundingClientRect()
          scrollViewRef.current.scrollTo({
            top: rect.top - 8,
            behavior: 'smooth',
          })
        }
      },
    }
  })

  return (
    <div className="rounded-base-12 bg-white px-base-12 py-base-16" ref={rootRef}>
      <h3 className="mb-base-16 font-miSansDemiBold450 text-lg leading-[21px]">{title}</h3>

      {/* 自提门店信息 */}
      {pickupStoreInfo ? (
        <PickupStoreCard storeInfo={pickupStoreInfo} storeNameStyle="font-miSansMedium380" />
      ) : null}

      {/* 商品列表 */}
      <div className={`mt-[20px] ${products.length > 1 ? 'flex flex-col gap-8' : ''}`}>
        {products.map((product) => (
          <div
            key={product.uid}
            className={`${products.length > 1 ? 'pb-8' : ''} ${product !== products[products.length - 1] ? 'border-b border-gray-base' : ''}`}>
            <ProductView
              id={product.uid}
              productData={product as unknown as RawProduct}
              quantity={product.quantity}
              showStatus={false}
              isEdit={false}
            />
          </div>
        ))}
      </div>

      {/* 自提信息输入 */}
      {showType === 'pickup' && (
        <div className="mt-[20px] flex flex-col gap-3">
          <div className="flex flex-col gap-4">
            <div className="flex items-center justify-between pb-3">
              <span className="text-base text-[#444446]">{getI18nString('phone_number')}</span>
              <div className="flex items-center gap-3">
                <Controller
                  control={control}
                  name="telephone"
                  render={({ field: { onChange, value } }) => (
                    <input
                      type="number"
                      className="w-72 text-right text-base outline-none placeholder:text-gray-400"
                      maxLength={11}
                      autoFocus
                      placeholder={getI18nString('only_china_phone')}
                      onBlur={() => {
                        // 只有在用户已经开始交互后才显示验证错误
                        if (hasInteracted && !value?.trim()) {
                          toast.show({
                            content: getI18nString('form_required_phone'),
                          })
                          return
                        }
                        if (hasInteracted) {
                          onBlurHandler('telephone')
                        }
                      }}
                      onChange={(e) => {
                        // 标记用户已经开始交互
                        if (!hasInteracted) {
                          setHasInteracted(true)
                        }
                        onChange(e.target.value?.trim()?.slice(0, 11) ?? '')
                      }}
                      value={value}
                    />
                  )}
                />
                <span className="text-primary">{getI18nString('required_text')}</span>
              </div>
            </div>

            <div className="flex items-center justify-between pb-3">
              <span className="text-base text-[#444446]">{getI18nString('comment')}</span>
              <Controller
                control={control}
                name="comment"
                render={({ field: { onChange, value } }) => (
                  <input
                    className="w-72 text-right text-base outline-none placeholder:text-gray-400"
                    maxLength={500}
                    placeholder={getI18nString('suggest_comment')}
                    onBlur={() => onBlurHandler('comment')}
                    onChange={(e) => onChange(e.target.value)}
                    value={value}
                  />
                )}
              />
            </div>
          </div>
        </div>
      )}
    </div>
  )
})
