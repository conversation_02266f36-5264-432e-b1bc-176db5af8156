/**
 * iOS滚动优化工具函数
 * 专门解决iPhone浏览器快速滑动时的白屏问题
 */

// 检测是否为iOS设备
export const isIOS = (): boolean => {
  if (typeof window === 'undefined') return false
  return /iPad|iPhone|iPod/.test(navigator.userAgent) || 
         (navigator.platform === 'MacIntel' && navigator.maxTouchPoints > 1)
}

// 检测是否为Safari浏览器
export const isSafari = (): boolean => {
  if (typeof window === 'undefined') return false
  return /^((?!chrome|android).)*safari/i.test(navigator.userAgent)
}

// 防抖函数
export const debounce = <T extends (...args: any[]) => any>(
  func: T,
  wait: number,
  immediate?: boolean
): ((...args: Parameters<T>) => void) => {
  let timeout: NodeJS.Timeout | null = null
  
  return function executedFunction(...args: Parameters<T>) {
    const later = () => {
      timeout = null
      if (!immediate) func(...args)
    }
    
    const callNow = immediate && !timeout
    
    if (timeout) clearTimeout(timeout)
    timeout = setTimeout(later, wait)
    
    if (callNow) func(...args)
  }
}

// 节流函数
export const throttle = <T extends (...args: any[]) => any>(
  func: T,
  limit: number
): ((...args: Parameters<T>) => void) => {
  let inThrottle: boolean
  
  return function executedFunction(...args: Parameters<T>) {
    if (!inThrottle) {
      func(...args)
      inThrottle = true
      setTimeout(() => inThrottle = false, limit)
    }
  }
}

// 强制重绘函数 - 解决iOS白屏问题
export const forceRepaint = (element?: HTMLElement): void => {
  const target = element || document.body
  if (!target) return
  
  // 方法1: 临时改变display属性
  const originalDisplay = target.style.display
  target.style.display = 'none'
  // 强制回流
  target.offsetHeight
  target.style.display = originalDisplay
}

// 更温和的强制重绘方法
export const gentleForceRepaint = (element?: HTMLElement): void => {
  const target = element || document.body
  if (!target) return
  
  // 方法2: 使用transform触发重绘
  const originalTransform = target.style.transform
  target.style.transform = 'translateZ(0)'
  requestAnimationFrame(() => {
    target.style.transform = originalTransform
  })
}

// 自定义平滑滚动函数 - 避免iOS上smooth滚动的问题
export const customSmoothScroll = (
  targetY: number,
  duration: number = 600,
  onComplete?: () => void
): void => {
  const startY = window.scrollY
  const distance = targetY - startY
  const startTime = performance.now()
  
  // 缓动函数
  const easeInOutCubic = (t: number): number => {
    return t < 0.5 ? 4 * t * t * t : (t - 1) * (2 * t - 2) * (2 * t - 2) + 1
  }
  
  const animateScroll = (currentTime: number) => {
    const timeElapsed = currentTime - startTime
    const progress = Math.min(timeElapsed / duration, 1)
    
    const easedProgress = easeInOutCubic(progress)
    const currentY = startY + distance * easedProgress
    
    window.scrollTo(0, currentY)
    
    if (progress < 1) {
      requestAnimationFrame(animateScroll)
    } else {
      onComplete?.()
    }
  }
  
  requestAnimationFrame(animateScroll)
}

// 优化的滚动监听器创建函数
export const createOptimizedScrollListener = (
  callback: () => void,
  options: {
    throttleMs?: number
    debounceMs?: number
    passive?: boolean
  } = {}
): {
  listener: () => void
  cleanup: () => void
} => {
  const {
    throttleMs = 16, // 约60fps
    debounceMs = 0,
    passive = true
  } = options
  
  let optimizedCallback = callback
  
  // 应用节流
  if (throttleMs > 0) {
    optimizedCallback = throttle(optimizedCallback, throttleMs)
  }
  
  // 应用防抖
  if (debounceMs > 0) {
    optimizedCallback = debounce(optimizedCallback, debounceMs)
  }
  
  const cleanup = () => {
    window.removeEventListener('scroll', optimizedCallback)
  }
  
  // 添加监听器
  window.addEventListener('scroll', optimizedCallback, { passive })
  
  return {
    listener: optimizedCallback,
    cleanup
  }
}

// 元素位置缓存管理
class ElementPositionCache {
  private cache = new Map<string, { top: number, timestamp: number }>()
  private readonly CACHE_DURATION = 100 // 缓存100ms
  
  getElementTop(element: HTMLElement, key: string): number {
    const now = Date.now()
    const cached = this.cache.get(key)
    
    // 如果缓存存在且未过期，返回缓存值
    if (cached && (now - cached.timestamp) < this.CACHE_DURATION) {
      return cached.top
    }
    
    // 获取新值并缓存
    const top = element.offsetTop
    this.cache.set(key, { top, timestamp: now })
    
    return top
  }
  
  clearCache(): void {
    this.cache.clear()
  }
}

export const elementPositionCache = new ElementPositionCache()

// iOS特定的滚动优化样式应用
export const applyIOSScrollOptimization = (element: HTMLElement): void => {
  if (!isIOS()) return
  
  // 应用iOS优化样式
  element.style.webkitOverflowScrolling = 'auto' // 避免touch导致的问题
  element.style.overscrollBehavior = 'contain'
  element.style.touchAction = 'pan-y'
  
  // 强制硬件加速，但避免过度使用
  element.style.transform = 'translateZ(0)'
  element.style.willChange = 'scroll-position'
}

// 移除iOS滚动优化样式
export const removeIOSScrollOptimization = (element: HTMLElement): void => {
  element.style.webkitOverflowScrolling = ''
  element.style.overscrollBehavior = ''
  element.style.touchAction = ''
  element.style.transform = ''
  element.style.willChange = ''
}

// 检测并修复白屏问题
export const detectAndFixWhiteScreen = (): void => {
  if (!isIOS() || !isSafari()) return
  
  // 延迟检测，确保页面已渲染
  setTimeout(() => {
    // 检测页面是否可能出现白屏
    const bodyHeight = document.body.scrollHeight
    const viewportHeight = window.innerHeight
    
    // 如果页面内容高度异常小，可能是白屏
    if (bodyHeight < viewportHeight * 0.5) {
      console.warn('Potential white screen detected, applying fix...')
      gentleForceRepaint()
    }
  }, 50)
}
