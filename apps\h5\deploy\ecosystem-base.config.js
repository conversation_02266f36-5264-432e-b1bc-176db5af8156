const pkg = require('../package.json')

module.exports = {
  // 根目录
  cwd: './',
  // 是否监听文件变动然后重启
  watch: false,
  // 不用监听的文件
  ignore_watch: ['node_modules'],
  // 应用启动模式，支持 fork 和 cluster 模式，默认cluster
  exec_mode: 'cluster',
  // 应用启动实例个数，仅在 cluster 模式有效
  instances: 1,
  // 指定日志文件的时间格式
  log_date_format: 'YYYY-MM-DD HH:mm:ss',
  // 错误日志文件
  error_file: `./deploy/logs/${pkg.name}-err.log`,
  // 正常日志文件
  out_file: `./deploy/logs/${pkg.name}-out.log`,
  // 日志合并
  merge_logs: true,
  // 应用运行少于时间被认为是异常启动
  min_uptime: '60s',
  // 最大异常重启次数
  max_restarts: 30,
  // 默认为 true, 发生异常的情况下自动重启
  autorestart: true,
  // 最大内存
  max_memory_restart: '300M',
  // crontab时间格式重启应用，目前只支持cluster模式
  cron_restart: '0 0 3 * *',
  // 异常重启情况下，延时重启时间
  restart_delay: '100',
  // node args
  node_args: [],
  // 在 PM2 Runtime 上实现了新的重启模式, 指数退避重启延迟
  exp_backoff_restart_delay: 100,
  // source map支持
  source_map_support: true,
  // pid保存路径
  pid_file: `./deploy/.pm2/pids/${pkg.name}.pid`,
}
