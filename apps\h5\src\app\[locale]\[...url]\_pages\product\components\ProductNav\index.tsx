'use client'

import { ReactNode, useCallback, useEffect, useRef, useState } from 'react'
import { useRouter } from 'next/navigation'
import { NavBar } from 'antd-mobile'
import clsx from 'clsx'

import { Arrow } from '@/components/icons'
import {
  createOptimizedScrollListener,
  customSmoothScroll,
  elementPositionCache,
  gentleForceRepaint,
  isIOS,
} from '@/utils/iosScrollOptimization'

type ProductNavBarProps = {
  title?: string
  left?: ReactNode
  right?: ReactNode
  onBack?: () => void
  customStyle?: React.CSSProperties
  productRef?: React.RefObject<HTMLDivElement>
  reviewRef?: React.RefObject<HTMLDivElement>
  detailRef?: React.RefObject<HTMLDivElement>
  recommendRef?: React.RefObject<HTMLDivElement>
  isProduct?: boolean
  hasReview?: boolean
  hasDetail?: boolean
  hasRecommend?: boolean
}

const ProductNavBar = ({
  title,
  left,
  right,
  onBack,
  customStyle = {},
  productRef,
  reviewRef,
  detailRef,
  recommendRef,
  isProduct,
  hasReview = true,
  hasDetail = true,
  hasRecommend = false,
}: ProductNavBarProps) => {
  const router = useRouter()
  const [activeTab, setActiveTab] = useState('product')
  const [showTabs, setShowTabs] = useState(false)
  const scrollListenerRef = useRef<{ cleanup: () => void } | null>(null)
  const isScrollingRef = useRef(false)

  const handleBack = () => {
    if (onBack) {
      onBack()
    } else {
      router.back()
    }
  }

  // 优化的滚动事件处理
  const handleScroll = useCallback(() => {
    if (!productRef?.current || isScrollingRef.current) return

    try {
      const { top } = productRef.current.getBoundingClientRect()
      // 当商品区域完全滚出视图时显示吸顶导航
      setShowTabs(top < -300)

      // 根据滚动位置更新当前激活的标签
      const scrollPosition = window.scrollY + 100 // 添加偏移量以提前触发

      // 使用缓存的位置信息，减少DOM查询
      const reviewPosition = reviewRef?.current
        ? elementPositionCache.getElementTop(reviewRef.current, 'review')
        : 0
      const detailPosition = detailRef?.current
        ? elementPositionCache.getElementTop(detailRef.current, 'detail')
        : 0
      const recommendPosition = recommendRef?.current
        ? elementPositionCache.getElementTop(recommendRef.current, 'recommend')
        : 0

      if (detailPosition < scrollPosition && scrollPosition >= recommendPosition && hasRecommend) {
        setActiveTab('recommend')
      } else if (detailPosition < scrollPosition && scrollPosition >= reviewPosition && hasReview) {
        setActiveTab('review')
      } else if (scrollPosition >= detailPosition && hasDetail) {
        setActiveTab('detail')
      } else {
        setActiveTab('product')
      }
    } catch (error) {
      console.warn('Error in scroll handler:', error)
      // iOS白屏修复
      if (isIOS()) {
        gentleForceRepaint()
      }
    }
  }, [productRef, detailRef, reviewRef, hasReview, hasDetail, recommendRef, hasRecommend])

  // 处理滚动事件
  useEffect(() => {
    if (!isProduct) return

    // 清理之前的监听器
    if (scrollListenerRef.current) {
      scrollListenerRef.current.cleanup()
    }

    // 创建优化的滚动监听器
    scrollListenerRef.current = createOptimizedScrollListener(handleScroll, {
      throttleMs: 16, // 60fps
      passive: true,
    })

    return () => {
      if (scrollListenerRef.current) {
        scrollListenerRef.current.cleanup()
        scrollListenerRef.current = null
      }
      // 清理位置缓存
      elementPositionCache.clearCache()
    }
  }, [isProduct, handleScroll])

  // 处理标签点击 - 使用优化的滚动
  const handleTabClick = useCallback(
    (tab: string) => {
      const refs = {
        product: productRef,
        review: reviewRef,
        detail: detailRef,
        recommend: recommendRef,
      }

      const targetRef = refs[tab as keyof typeof refs]
      if (!targetRef?.current) return

      // 标记正在滚动，避免滚动监听器干扰
      isScrollingRef.current = true
      setActiveTab(tab)

      const targetPosition = Math.max(0, targetRef.current.offsetTop - 45) // 减去导航栏高度

      // 使用自定义平滑滚动，避免iOS上的问题
      customSmoothScroll(targetPosition, 400, () => {
        // 滚动完成后重置状态
        isScrollingRef.current = false

        // iOS白屏修复
        if (isIOS()) {
          setTimeout(() => {
            gentleForceRepaint()
          }, 50)
        }
      })
    },
    [productRef, reviewRef, detailRef, recommendRef],
  )

  return (
    <div className="sticky top-0 z-50">
      <NavBar
        backIcon={left ? left : <Arrow size={24} color="#000000" rotate={180} />}
        onBack={handleBack}
        right={right && <div className="float-end">{right}</div>}
        style={{ ...customStyle, backgroundColor: '#FFFFFF', '--height': '5.6rem' }}>
        {isProduct && showTabs ? (
          <div className="flex items-center justify-around gap-8">
            <button
              className={clsx('px-2 py-1', activeTab === 'product' && 'text-primary')}
              onClick={() => handleTabClick('product')}>
              商品
            </button>
            {hasReview && (
              <button
                className={clsx('px-2 py-1', activeTab === 'review' && 'text-primary')}
                onClick={() => handleTabClick('review')}>
                评价
              </button>
            )}
            {hasDetail && (
              <button
                className={clsx('px-2 py-1', activeTab === 'detail' && 'text-primary')}
                onClick={() => handleTabClick('detail')}>
                详情
              </button>
            )}

            {hasRecommend && (
              <button
                data-a={activeTab}
                className={clsx('px-2 py-1', activeTab === 'recommend' && 'text-primary')}
                onClick={() => handleTabClick('recommend')}>
                推荐
              </button>
            )}
          </div>
        ) : (
          <span>{title}</span>
        )}
      </NavBar>
    </div>
  )
}

export default ProductNavBar
