'use client'

import React, { useEffect, useMemo, useRef, useState } from 'react'
import { useTranslations } from 'next-intl'
import { OrderPickupInfo, OrderVouchers } from '@ninebot/core'
import { Image, Swiper } from 'antd-mobile'
import { SwiperRef } from 'antd-mobile/es/components/swiper'

import { Arrow, CustomButton, CustomPopup } from '@/components'

type CouponPopupProps = {
  /** 是否显示弹窗 */
  popupVisible: boolean
  /** 拷贝到剪贴板的回调函数 */
  copyToClipboard: (text: string) => void
  /** 关闭弹窗的回调函数 */
  closePopup: () => void
  /** 自提取货码信息 */
  pickupInfo?: OrderPickupInfo
  /** 券码列表 */
  couponList?: OrderVouchers
}

type CarouselItem = {
  image_url: string
  status?: string
  status_label?: string
  imageStyle?: {
    opacity?: number
  }
}

/**
 * 券码/取货码弹窗组件
 * 支持两种模式：
 * 1. 取货码模式：显示单个取货码及其二维码
 * 2. 券码模式：显示多个券码及其二维码，支持轮播切换
 */
const CouponPopup = (props: CouponPopupProps) => {
  const { popupVisible, copyToClipboard, closePopup, pickupInfo, couponList = [] } = props
  const getI18nString = useTranslations('Common')
  const swiperRef = useRef<SwiperRef>(null)
  const [currentIndex, setCurrentIndex] = useState(0)
  const [isAnimating, setIsAnimating] = useState(false)

  /**
   * 判断当前是否为取货码模式
   */
  const isPickupCode = useMemo(() => {
    return !!pickupInfo
  }, [pickupInfo])

  /**
   * 获取当前显示的验证码
   */
  const currentCode = useMemo(() => {
    return isPickupCode ? pickupInfo?.code : couponList?.[currentIndex]?.code
  }, [isPickupCode, pickupInfo?.code, couponList, currentIndex])

  /**
   * 获取当前验证码的状态
   */
  const isUnUsed = useMemo(() => {
    if (isPickupCode) {
      return pickupInfo?.status === '0'
    }
    return couponList?.[currentIndex]?.status === '0'
  }, [isPickupCode, pickupInfo?.status, couponList, currentIndex])

  /**
   * 构建轮播图数据
   * 取货码模式：返回单个取货码的二维码数据
   * 券码模式：返回券码列表的二维码数据
   */
  const carouselData = useMemo(() => {
    if (isPickupCode && pickupInfo) {
      return [
        {
          image_url: pickupInfo.qr_code,
          status: pickupInfo.status,
          status_label: pickupInfo.status_label,
          imageStyle: pickupInfo.status === '0' ? {} : { opacity: 0.08 },
        },
      ]
    }

    return couponList
      ?.map((coupon) => {
        if (!coupon) return null
        return {
          image_url: coupon.qr_code,
          status: coupon.status,
          status_label: coupon.status_label,
          imageStyle: coupon.status === '0' ? {} : { opacity: 0.08 },
        }
      })
      .filter(Boolean) as CarouselItem[]
  }, [isPickupCode, pickupInfo, couponList])

  /**
   * 处理轮播图切换动画
   */
  const handleSwipeAnimation = () => {
    setIsAnimating(true)
    setTimeout(() => setIsAnimating(false), 300) // 动画结束后重置状态
  }

  /**
   * 切换到上一张
   */
  const handlePrevPress = () => {
    if (currentIndex > 0 && !isAnimating) {
      swiperRef.current?.swipePrev()
      setCurrentIndex((prev) => Math.max(prev - 1, 0))
      handleSwipeAnimation()
    }
  }

  /**
   * 切换到下一张
   */
  const handleNextPress = () => {
    if (currentIndex < Number(carouselData?.length) - 1 && !isAnimating) {
      swiperRef.current?.swipeNext()
      setCurrentIndex((prev) => Math.min(prev + 1, Number(carouselData?.length) - 1))
      handleSwipeAnimation()
    }
  }

  /**
   * 重置轮播图状态
   */
  useEffect(() => {
    if (!popupVisible) {
      swiperRef.current?.swipeTo(0)
      setCurrentIndex(0)
    }
  }, [popupVisible])

  /**
   * 渲染验证码显示区域
   */
  const renderCodeSection = () => (
    <div className="mt-4 flex items-center justify-between">
      <div className="font-miSansRegular330 text-lg leading-[22px] text-[#0F0F0F]">
        {getI18nString('verification_code_index', { key: currentIndex + 1 })}
      </div>
      <div className="flex items-center font-miSansRegular330">
        <div
          className={`text-base leading-[22px] ${
            isUnUsed ? 'text-[#86868B]' : 'text-[#AEAEB2] line-through'
          }`}>
          {currentCode}
        </div>
        <div
          className={`text-base leading-[22px] ${isUnUsed ? 'text-[#86868B]' : 'text-[#AEAEB2]'}`}>
          ｜
        </div>
        <CustomButton
          customStyle={{ width: 'auto' }}
          fill="none"
          onClick={() => copyToClipboard(currentCode || '')}>
          <div
            className={`text-base leading-[22px] ${isUnUsed ? 'text-[#0F0F0F]' : 'text-[#AEAEB2]'}`}>
            {getI18nString('copy')}
          </div>
        </CustomButton>
      </div>
    </div>
  )

  /**
   * 渲染轮播图区域
   */
  const renderCarouselSection = () => (
    <div className="mt-4 flex flex-row items-center justify-center">
      {Number(carouselData?.length) > 1 && (
        <CustomButton
          customStyle={{
            width: 32,
            height: 32,
            borderRadius: 16,
            justifyContent: 'center',
            alignItems: 'center',
            backgroundColor: currentIndex === 0 ? 'rgba(0, 0, 0, 0.1)' : 'rgba(0, 0, 0, 0.4)',
          }}
          onClick={handlePrevPress}>
          <Arrow color="white" size={14} rotate={-180} />
        </CustomButton>
      )}

      <div className="mx-5 h-[200px] w-[200px]">
        <Swiper ref={swiperRef} loop={false} indicator={() => null}>
          {carouselData?.filter(Boolean).map((item, index) => (
            <Swiper.Item key={index}>
              <div>
                <Image
                  className={`h-full w-full ${item?.status !== '0' ? 'opacity-[0.08]' : ''}`}
                  src={item?.image_url || ''}
                  fit="cover"
                  alt=""
                />
                {item?.status !== '0' && (
                  <div className="absolute left-[50%] top-[91px] -translate-x-1/2 self-center rounded border border-primary px-[33px] py-2">
                    <div className="text-xs leading-4 text-primary">{item?.status_label}</div>
                  </div>
                )}
              </div>
            </Swiper.Item>
          ))}
        </Swiper>
      </div>

      {Number(carouselData?.length) > 1 && (
        <CustomButton
          customStyle={{
            width: 32,
            height: 32,
            borderRadius: 16,
            justifyContent: 'center',
            alignItems: 'center',
            backgroundColor:
              currentIndex === Number(carouselData?.length) - 1
                ? 'rgba(0, 0, 0, 0.1)'
                : 'rgba(0, 0, 0, 0.4)',
          }}
          onClick={handleNextPress}>
          <Arrow color="white" size={14} />
        </CustomButton>
      )}
    </div>
  )

  return (
    <CustomPopup
      visible={popupVisible}
      onClose={closePopup}
      showHeader
      headTitle={getI18nString('coupon_code')}>
      <div className="p-base-24 pt-[16px]">
        {/* 标题区域 */}
        <div className="font-miSansDemiBold450 text-lg leading-[21px]">
          {getI18nString('verification_code_total')}
          <span className="text-primary">{isPickupCode ? '1' : couponList?.length}</span>
          {getI18nString('piece')}
        </div>

        {/* 验证码显示区域 */}
        {renderCodeSection()}

        {/* 券码过期时间显示（仅券码模式显示） */}
        {!isPickupCode && (
          <div className="font-miSansRegular330 text-base leading-[16px] text-[#8E8E93]">
            {getI18nString('expired_time_until')}
            {couponList?.[currentIndex]?.expired_at?.split(' ')[0]}
          </div>
        )}

        {/* 轮播图区域 */}
        {renderCarouselSection()}

        {/* 页码显示 */}
        <div className="mb-10 mt-[8px] text-center text-sm leading-[20px] text-black">
          {isPickupCode ? (
            <>
              <span className="text-base">1/</span>
              <span className="text-base">1</span>
            </>
          ) : (
            <>
              <span className="text-base">{currentIndex + 1}/</span>
              <span className="text-base">{couponList?.length}</span>
            </>
          )}
        </div>
        <div className="mt-16 text-center text-gray-3">
          <div className="text-sm">{pickupInfo?.expired_info}</div>
        </div>
      </div>
    </CustomPopup>
  )
}

export default CouponPopup
