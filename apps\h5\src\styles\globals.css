/* stylelint-disable at-rule-no-unknown */
@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  /* 默认值 */
  --nb-font-size: 10px;
  --adm-font-family: var(--font-family-miSansMedium380), 'Microsoft Yahei', sans-serif;
}

html {
  font-size: var(--nb-font-size, 10px);
  transition: font-size 0.2s ease-in-out;
  text-size-adjust: 100%; /* 禁用自动字体调整 */
  touch-action: manipulation;
  /* iOS滚动优化 */
  -webkit-text-size-adjust: 100%;
  -webkit-tap-highlight-color: transparent;
}

/* iOS Safari 特定优化 */
@supports (-webkit-touch-callout: none) {
  html {
    /* 避免iOS上的滚动问题 */
    -webkit-overflow-scrolling: auto;
    overscroll-behavior: contain;
  }

  body {
    /* 防止iOS滚动穿透和白屏 */
    overscroll-behavior: contain;
    -webkit-overflow-scrolling: auto;
    /* 强制硬件加速，但避免过度使用 */
    transform: translateZ(0);
    will-change: scroll-position;
  }
}

/* #app必须保留 */
#app {
  height: 100%;
}

/*========= 自定义antd-mobile组件样式 =========*/
@import url('./components/tabs.css');
@import url('./components/sidebar.css');
@import url('./components/dropdown.css');
@import url('./components/checkoutlist.css');
@import url('./components/form.css');
@import url('./components/checkbox.css');
@import url('./components/dialog.css');
@import url('./components//imageuploader.css');
@import url('./components/card.css');
@import url('./components/collapse.css');
@import url('./components/imageviewer.css');
@import url('./components/SwipeAction.css');
@import url('./components/button.css');
@import url('./components/navbar.css');

/*========= 自定义样式 =========*/

.max-container {
  width: 100%;
  max-width: 375px;
  margin: 0 auto;
  box-sizing: border-box;
}

/* 滚动容器优化 - 避免iOS白屏问题 */
.scroll-container {
  /* 避免使用-webkit-overflow-scrolling: touch，这可能导致iOS白屏 */
  -webkit-overflow-scrolling: auto;
  overscroll-behavior: contain;
  touch-action: pan-y;
}

.scroll-container::-webkit-scrollbar {
  height: 0; /* 隐藏水平滚动条 */
}

.scroll-container::-webkit-scrollbar-thumb {
  background-color: transparent; /* 可选：设置滑块为透明，确保不会显示 */
}

/* iOS特定的滚动优化类 */
.ios-scroll-fix {
  -webkit-overflow-scrolling: auto;
  overscroll-behavior: contain;
  touch-action: pan-y;
  transform: translateZ(0);
  will-change: scroll-position;
}

@layer utilities {
  .no-scrollbar::-webkit-scrollbar {
    display: none;
  }
  .no-scrollbar {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }
}

.search-input .adm-input-clear {
  padding: 0.8rem;
}

.adm-badge .adm-badge-content {
  font-size: 1.2rem;
}

.adm-error-block .adm-error-block-image {
  display: flex;
  align-items: center;
  justify-content: center;
}

.adm-stepper-middle {
  border-left: 1px solid #f3f3f4 !important;
  border-right: 1px solid #f3f3f4 !important;
}

.adm-stepper-minus:disabled,
.adm-stepper-plus:disabled {
  background: #fff;
}

.adm-error-block .adm-error-block-description {
  margin-top: 0px !important;
}

.adm-error-block .adm-error-block-description-title {
  display: none;
}
