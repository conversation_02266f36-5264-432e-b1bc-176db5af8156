'use client'

import { useMemo } from 'react'
import { useTranslations } from 'next-intl'
import defaultUserImage from '@public/images/default_user.png'
import {
  Customer,
  loggedOut,
  setOrderFilterStatus,
  storeConfigSelector,
  useLazyGetLogoutUrlQuery,
} from '@ninebot/core'
import { useAppDispatch, useAppSelector } from '@ninebot/core/src/store/hooks'
import { Image, List } from 'antd-mobile'
import clsx from 'clsx'

import { Arrow } from '@/components'
import IcLicense from '@/components/icons/IcLicense'
import { useRouter } from '@/i18n/navigation'

import {
  AccountAddress,
  AccountCoupon,
  AccountReturn,
  Coin,
  Comment,
  Delivery,
  Email,
  Payment,
  Phone,
  Shipment,
} from './icons'

const AccountCenter = ({ customer }: { customer: Customer }) => {
  const getI18nString = useTranslations('Common')
  const router = useRouter()
  const dispatch = useAppDispatch()

  const storeConfig = useAppSelector(storeConfigSelector)
  const [getLogoutUrl] = useLazyGetLogoutUrlQuery()

  const {
    info,
    orders,
    services: { coupons },
  } = customer

  const activeCoupons = useMemo(() => coupons?.filter((item) => item?.status === 0), [coupons])

  const orderStatuses = [
    {
      key: 'pending',
      icon: <Payment />,
      label: getI18nString('unpaid'),
      status: 'pending',
      description: getI18nString('unpaid_desc', { key: orders.pending }),
    },
    {
      key: 'awaiting_shipment',
      icon: <Shipment />,
      label: getI18nString('awaiting_shipment'),
      status: 'processing',
      description: getI18nString('awaiting_shipment_desc', { key: orders.processing }),
    },
    {
      key: 'awaiting_receipt',
      icon: <Delivery />,
      label: getI18nString('awaiting_receipt'),
      status: 'to_be_used_shipped_pending_pickup',
      description: getI18nString('awaiting_receipt_desc', { key: orders.shipped }),
    },
    {
      key: 'completed',
      icon: <Comment />,
      // label: getI18nString('awaiting_review'),
      label: getI18nString('completed'),
      status: 'delivered_completed',
      description: getI18nString('completed_desc', { key: orders.complete }),
    },
  ]

  const acountServices = [
    {
      icon: <AccountReturn />,
      label: getI18nString('return_after'),
      key: 'return',
      right: <Arrow color="#6E6E73" />,
      url: '/customer/orders/',
    },
    {
      icon: <AccountCoupon />,
      label: getI18nString('my_coupon'),
      key: 'coupon',
      right:
        activeCoupons && activeCoupons?.length > 0 ? (
          <div className="flex flex-row items-center justify-between">
            <div className="rounded-[20px] bg-[#FEE5E5] px-[8px] py-[4px] font-miSansMedium380 text-[14px] leading-[16.8px] text-[#DA291C]">
              {activeCoupons.length} 张可用
            </div>
            <Arrow color="#6E6E73" />
          </div>
        ) : (
          <Arrow color="#6E6E73" />
        ),
      url: '/customer/coupons',
    },
    {
      icon: <AccountAddress />,
      label: getI18nString('my_shipping_address'),
      key: 'address',
      right: <Arrow color="#6E6E73" />,
      url: '/customer/addresses',
    },
    {
      icon: <IcLicense />,
      label: getI18nString('policy'),
      key: 'policy',
      right: <Arrow color="#6E6E73" />,
      url: `/${storeConfig?.compliance_policy}`,
    },
  ]

  const handleToOrders = (status: string) => {
    dispatch(setOrderFilterStatus(status))
    router.push('/customer/orders')
  }

  const handleLogout = async () => {
    try {
      const response = await getLogoutUrl({}).unwrap()

      // 创建隐藏的 iframe 来调用三方系统退出接口
      const iframe = document.createElement('iframe')
      iframe.style.display = 'none'
      iframe.src = response?.web_passport_logout_url?.url || ''
      iframe.onload = () => {
        console.log('三方系统退出接口调用成功')
        // 清理本地 token 和用户状态
        dispatch(loggedOut())
        // 移除 iframe
        document.body.removeChild(iframe)

        router.push('/')
      }
      iframe.onerror = () => {
        console.error('三方系统退出接口调用失败')
        // 即使失败也要清理本地数据
        dispatch(loggedOut())
        document.body.removeChild(iframe)

        router.push('/')
      }
      document.body.appendChild(iframe)
    } catch {
      router.push('/')
    }
  }

  return (
    <div className="flex flex-col gap-[8px] px-[12px]">
      {/* Account User Info */}
      <div className="flex w-full gap-[8px]">
        <div className="flex items-center gap-[12px] rounded-[20px] bg-white px-base-16 py-8">
          <Image
            style={{
              width: 56,
              height: 56,
            }}
            src={defaultUserImage.src}
            alt="default_user"
          />
          <div className="min-w-0 flex-1">
            {info.name && (
              <div className="mb-[4px] break-words font-miSansSemibold520 text-[18px] leading-[21.6px]">
                {info.name}
              </div>
            )}
            <div className="flex-row">
              {info.phone && (
                <div className="flex items-center gap-[4px] py-[4px]">
                  <Phone />
                  <span className="break-all text-[10px] leading-[12px] text-[#6E6E73]">
                    {info.phone}
                  </span>
                </div>
              )}
              {info.email && (
                <div className="flex items-center gap-[4px] pt-[4px]">
                  <Email />
                  <span className="break-all text-[10px] leading-[12px] text-[#6E6E73]">
                    {info.email}
                  </span>
                </div>
              )}
            </div>
          </div>
        </div>
        <div className="flex w-[108px] flex-col justify-center rounded-[20px] bg-[#DA291C] px-base-12 py-8">
          <div className="font-[380] text-white">{getI18nString('my_n-coins')}</div>
          <div className="mt-[8px] flex items-center gap-[4px]">
            <Coin />
            <span
              className={clsx(
                'text-white',
                info.nCoin && info.nCoin.toString().length > 4
                  ? 'font-miSansDemiBold450 text-base'
                  : 'font-miSansSemibold520 text-[22px]',
              )}>
              {info.nCoin}
            </span>
          </div>
        </div>
      </div>
      {/* Account Order*/}
      <div className="flex flex-col gap-8 rounded-[20px] bg-white px-[12px] pb-[12px] pt-8">
        <div className="flex flex-row items-center justify-between px-[4px]">
          <span className="font-miSansDemiBold450 text-[18px] leading-[21.6px]">
            {getI18nString('my_orders')}
          </span>
          <div
            className="flex flex-row items-center justify-center gap-[4px]"
            onClick={() => handleToOrders('')}>
            <span className="font-miSansMedium380 text-[14px] leading-[16.8px]">
              {getI18nString('all')}
            </span>
            <div className="mt-[-1px]">
              <Arrow />
            </div>
          </div>
        </div>
        <div className="grid w-full grid-cols-2 gap-[8px]">
          {orderStatuses.map((status) => (
            <div
              key={status.label}
              className="flex w-full flex-col items-start gap-[8px] rounded-[16px] bg-[#F8F8F9] p-[12px]"
              onClick={() => handleToOrders(status.status)}>
              <div className="mb-[8px]">{status.icon}</div>
              <div className="flex flex-col">
                <div className="mb-[4px] font-miSansDemiBold450 text-[16px] leading-[22.4px]">
                  {status.label}
                </div>
                <div className="font-miSansRegular330 text-[12px] leading-[14.4px] text-[#6E6E73]">
                  {status.description.split(/(\d+)/).map((part, index) => (
                    <span
                      key={index}
                      className={
                        /\d+/.test(part)
                          ? 'font-miSansDemiBold450 text-[#DA291C]'
                          : 'font-miSansRegular330 text-[#6E6E73]'
                      }>
                      {part}
                    </span>
                  ))}
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
      {/* Account Services */}
      <div className="flex flex-col gap-[16px] rounded-[20px] bg-white pt-8">
        <div className="px-[16px] font-miSansDemiBold450 text-[18px] leading-[22px]">
          {getI18nString('service_tools')}
        </div>
        <List
          mode="card"
          className="account-center-list !my-0"
          style={{
            alignItems: 'flex-start',
          }}>
          {acountServices.map((item) => (
            <List.Item
              key={item.key}
              prefix={item.icon}
              style={{
                padding: '0 4px',
              }}
              arrowIcon={item.right}
              onClick={() => {
                if (item.key === 'return') {
                  dispatch(setOrderFilterStatus('order_requisition'))
                }
                router.push(item.url)
              }}>
              <div className="font-miSansMedium380 text-[16px] leading-[24px] text-[#444446]">
                {item.label}
              </div>
            </List.Item>
          ))}
        </List>
      </div>

      <div className="mt-[24px] flex flex-1 items-center justify-center">
        <div
          className="font-miSansDemiBold450 text-lg leading-[20px] text-[#DA291C]"
          onClick={handleLogout}>
          {getI18nString('account_logout')}
        </div>
      </div>
    </div>
  )
}

export default AccountCenter
